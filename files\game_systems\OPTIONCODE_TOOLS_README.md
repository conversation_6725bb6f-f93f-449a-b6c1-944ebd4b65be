# 🛠️ Option Code Tools

## คำอธิบาย
เครื่องมือสำหรับจัดการ Option Code จากไฟล์ XML และเชื่อมโยงกับข้อมูล Force Code ในฐานข้อมูล

## ไฟล์ที่เกี่ยวข้อง

### 1. **optioncode_manager.php**
หน้าหลักสำหรับจัดการ Option Code
- แสดงข้อมูลจาก optioncode.xml
- เชื่อมโยงกับตาราง WEB_cabal_forcecodes
- แสดงสถิติและการเชื่อมโยงข้อมูล

### 2. **class_module/optioncode_api.php**
API สำหรับประมวลผลข้อมูล
- `load_data`: โหลดข้อมูลจาก XML และเชื่อมโยงกับ Database
- `get_force_codes`: ดึงข้อมูลจากตาราง WEB_cabal_forcecodes
- `search_code`: ค้นหา Force Code

### 3. **test_optioncode_tools.php**
หน้าทดสอบระบบ
- ทดสอบการอ่านไฟล์ XML
- ทดสอบการเชื่อมต่อ Database
- ทดสอบ API
- ทดสอบการเชื่อมโยงข้อมูล

## โครงสร้างฐานข้อมูล

### ตาราง: WEB_cabal_forcecodes
```sql
CREATE TABLE [dbo].[WEB_cabal_forcecodes] (
    [ForceCode] int NOT NULL,
    [OptionScroll] int NOT NULL,
    [OptionScrollName] nvarchar(1000) COLLATE Thai_CI_AS NOT NULL,
    [ForceCodeName] nvarchar(1000) COLLATE Thai_CI_AS NULL
)
```

### ฟิลด์อธิบาย:
- **ForceCode**: รหัส Force Code (ตรงกับ code ใน XML)
- **OptionScroll**: รหัส Option Scroll
- **OptionScrollName**: ชื่อ Option Scroll
- **ForceCodeName**: ชื่อ Force Code

## โครงสร้างไฟล์ XML

### รูปแบบ optioncode.xml:
```xml
type="AMULET"		
    index="1"	code="5"
    index="2"	code="25"
    index="3"	code="36"
    ...

type="BELT"		
    index="1"	code="1"
    index="2"	code="3"
    ...
```

### คำอธิบาย:
- **type**: ประเภทไอเท็ม (AMULET, BELT, EARRING, etc.)
- **index**: ลำดับของ option
- **code**: รหัส Force Code (เชื่อมโยงกับ ForceCode ในฐานข้อมูล)

## การใช้งาน

### 1. การทดสอบระบบ
```
?url=game_systems/test_optioncode_tools
```
- ทดสอบการอ่านไฟล์ XML
- ทดสอบการเชื่อมต่อฐานข้อมูล
- ทดสอบ API ทั้งหมด
- ตรวจสอบการเชื่อมโยงข้อมูล

### 2. การใช้งานหลัก
```
?url=game_systems/optioncode_manager
```

#### แท็บ Option Codes:
- แสดงข้อมูลจาก XML
- เชื่อมโยงกับชื่อ Force Code
- กรองข้อมูล (ทั้งหมด/เชื่อมโยงแล้ว/ยังไม่เชื่อมโยง)
- ค้นหาข้อมูล

#### แท็บ Force Codes Database:
- แสดงข้อมูลจากตาราง WEB_cabal_forcecodes
- ค้นหา Force Code
- รีเฟรชข้อมูล

#### แท็บการเชื่อมโยง:
- สรุปการเชื่อมโยงข้อมูล
- แสดงเปอร์เซ็นต์การเชื่อมโยงแต่ละประเภท
- Progress bar แสดงสถานะ

## ฟีเจอร์หลัก

### 📊 สถิติ
- จำนวนประเภทไอเท็ม
- จำนวนรหัสทั้งหมด
- จำนวนที่เชื่อมโยงแล้ว
- จำนวนที่ยังไม่เชื่อมโยง

### 🔍 การค้นหาและกรอง
- ค้นหาตามประเภทไอเท็ม
- ค้นหาตามรหัส
- ค้นหาตามชื่อ Force Code
- กรองตามสถานะการเชื่อมโยง

### 🎨 การแสดงผล
- สีเขียว: เชื่อมโยงแล้ว
- สีเหลือง: ยังไม่เชื่อมโยง
- Progress bar แสดงเปอร์เซ็นต์
- Responsive design

### 🔄 การรีเฟรช
- รีเฟรชข้อมูล XML
- รีเฟรชข้อมูล Database
- อัพเดทสถิติอัตโนมัติ

## การแก้ไขปัญหา

### ปัญหาที่อาจเกิดขึ้น:

#### 1. ไม่พบไฟล์ optioncode.xml
```
เกิดข้อผิดพลาด: ไม่พบไฟล์ optioncode.xml
```
**วิธีแก้:** ตรวจสอบว่าไฟล์ optioncode.xml อยู่ในโฟลเดอร์ files/game_systems/

#### 2. ไม่สามารถเชื่อมต่อฐานข้อมูลได้
```
เกิดข้อผิดพลาด: ไม่สามารถดึงข้อมูลได้
```
**วิธีแก้:** 
- ตรวจสอบการตั้งค่าฐานข้อมูลใน _app/dbinfo.inc.php
- ตรวจสอบว่าตาราง WEB_cabal_forcecodes มีอยู่

#### 3. ข้อมูลไม่เชื่อมโยง
```
แสดง "ไม่พบข้อมูล" ในคอลัมน์ Force Code Name
```
**วิธีแก้:**
- ตรวจสอบว่า code ใน XML ตรงกับ ForceCode ในฐานข้อมูล
- เพิ่มข้อมูลในตาราง WEB_cabal_forcecodes

## ตัวอย่างข้อมูล

### ข้อมูลตัวอย่างในฐานข้อมูล:
```sql
INSERT INTO [dbo].[WEB_cabal_forcecodes] VALUES 
(1, 129, 'HP', 'HP'),
(2, 257, 'MP', 'MP'),
(5, 385, 'Attack Rate', 'Attack Rate'),
(25, 409, 'Critical Rate', 'Critical Rate');
```

### ข้อมูลตัวอย่างใน XML:
```xml
type="AMULET"		
    index="1"	code="5"     <!-- จะเชื่อมโยงกับ Attack Rate -->
    index="2"	code="25"    <!-- จะเชื่อมโยงกับ Critical Rate -->
    index="3"	code="36"    <!-- ไม่พบในฐานข้อมูล = "ไม่พบข้อมูล" -->
```

## การพัฒนาเพิ่มเติม

### ฟีเจอร์ที่สามารถเพิ่มได้:
1. **การแก้ไขข้อมูล**: เพิ่ม/แก้ไข Force Code ผ่านหน้าเว็บ
2. **การส่งออกข้อมูล**: Export เป็น Excel/CSV
3. **การนำเข้าข้อมูล**: Import ข้อมูลจากไฟล์
4. **ประวัติการเปลี่ยนแปลง**: Log การแก้ไขข้อมูล
5. **การแจ้งเตือน**: แจ้งเตือนเมื่อมีข้อมูลไม่เชื่อมโยง

## การติดตั้ง

### ขั้นตอนการติดตั้ง:
1. วางไฟล์ทั้งหมดในโฟลเดอร์ files/game_systems/
2. ตรวจสอบว่าไฟล์ optioncode.xml มีอยู่
3. ตรวจสอบการเชื่อมต่อฐานข้อมูล
4. รันการทดสอบที่ test_optioncode_tools.php
5. เริ่มใช้งานที่ optioncode_manager.php

## การบำรุงรักษา

### การบำรุงรักษาประจำ:
1. **สำรองข้อมูล**: สำรองไฟล์ XML และฐานข้อมูล
2. **ตรวจสอบข้อมูล**: ตรวจสอบการเชื่อมโยงข้อมูลเป็นประจำ
3. **อัพเดทข้อมูล**: อัพเดท Force Code เมื่อมีการเปลี่ยนแปลง
4. **ทดสอบระบบ**: รันการทดสอบเป็นประจำ

---

## 📞 การติดต่อ
หากมีปัญหาหรือข้อสงสัย กรุณาติดต่อผู้ดูแลระบบ
