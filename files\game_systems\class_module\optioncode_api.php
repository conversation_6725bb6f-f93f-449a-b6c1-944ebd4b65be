<?php
session_start();
if (!isset($_SESSION['userLogin'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'ไม่มีการ login']);
    exit;
}

require_once("../../../_app/dbinfo.inc.php");

$conn = db_connect();

// รับข้อมูลจาก POST
$action = $_POST['action'] ?? '';

switch ($action) {
    case 'load_data':
        loadOptionCodeData($conn);
        break;
    
    case 'get_force_codes':
        getForceCodesFromDB($conn);
        break;
    
    case 'search_code':
        searchForceCode($conn);
        break;
    
    default:
        echo json_encode(['success' => false, 'message' => 'Action ไม่ถูกต้อง']);
        break;
}

function loadOptionCodeData($conn) {
    try {
        // อ่านไฟล์ XML
        $xmlFile = '../optioncode.xml';
        if (!file_exists($xmlFile)) {
            echo json_encode(['success' => false, 'message' => 'ไม่พบไฟล์ optioncode.xml']);
            return;
        }
        
        $xmlContent = file_get_contents($xmlFile);
        if (!$xmlContent) {
            echo json_encode(['success' => false, 'message' => 'ไม่สามารถอ่านไฟล์ XML ได้']);
            return;
        }
        
        // ดึงข้อมูล Force Codes จาก database
        $forceCodesMap = getForceCodesMap($conn);
        
        // แปลง XML เป็น array
        $optionData = parseOptionCodeXML($xmlContent, $forceCodesMap);
        
        // คำนวณสถิติ
        $stats = calculateStats($optionData);
        
        echo json_encode([
            'success' => true,
            'data' => $optionData,
            'stats' => $stats,
            'force_codes_count' => count($forceCodesMap)
        ]);
        
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => 'เกิดข้อผิดพลาด: ' . $e->getMessage()]);
    }
}

function getForceCodesMap($conn) {
    $forceCodesMap = [];
    
    try {
        $sql = "SELECT ForceCode, ForceCodeName, OptionScroll, OptionScrollName FROM WEB_cabal_forcecodes";
        $stmt = sqlsrv_query($conn, $sql);
        
        if ($stmt) {
            while ($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_ASSOC)) {
                $forceCodesMap[$row['ForceCode']] = [
                    'name' => $row['ForceCodeName'],
                    'optionScroll' => $row['OptionScroll'],
                    'optionScrollName' => $row['OptionScrollName']
                ];
            }
        }
    } catch (Exception $e) {
        error_log("Error getting force codes: " . $e->getMessage());
    }
    
    return $forceCodesMap;
}

function parseOptionCodeXML($xmlContent, $forceCodesMap) {
    $result = [];

    try {
        // แปลง XML string เป็น SimpleXMLElement
        $xml = simplexml_load_string($xmlContent);

        if ($xml === false) {
            throw new Exception('ไม่สามารถแปลง XML ได้');
        }

        // วนลูปผ่าน itemType ทั้งหมด
        foreach ($xml->itemType as $itemType) {
            $typeName = (string)$itemType['name'];
            $options = [];

            // วนลูปผ่าน option ทั้งหมดใน itemType นี้
            foreach ($itemType->option as $option) {
                $index = (string)$option['index'];
                $code = (string)$option['code'];

                // ค้นหาข้อมูล Force Code
                $forceCodeInfo = null;
                if ($code !== '' && isset($forceCodesMap[$code])) {
                    $forceCodeInfo = $forceCodesMap[$code];
                }

                $optionData = [
                    'index' => $index,
                    'code' => $code,
                    'forceCodeName' => $forceCodeInfo ? $forceCodeInfo['name'] : ($code !== '' ? 'ไม่พบข้อมูล' : null),
                    'optionScroll' => $forceCodeInfo ? $forceCodeInfo['optionScroll'] : null,
                    'optionScrollName' => $forceCodeInfo ? $forceCodeInfo['optionScrollName'] : null
                ];

                $options[] = $optionData;
            }

            $result[] = [
                'type' => $typeName,
                'options' => $options
            ];
        }

    } catch (Exception $e) {
        // ถ้า XML parsing ล้มเหลว ให้ลองใช้วิธีเดิม (regex)
        error_log("XML parsing failed, falling back to regex: " . $e->getMessage());
        return parseOptionCodeXMLFallback($xmlContent, $forceCodesMap);
    }

    return $result;
}

function parseOptionCodeXMLFallback($xmlContent, $forceCodesMap) {
    $lines = explode("\n", $xmlContent);
    $result = [];
    $currentType = '';

    foreach ($lines as $line) {
        $line = trim($line);

        // ตรวจสอบว่าเป็นบรรทัด type หรือไม่
        if (preg_match('/type="([^"]+)"/', $line, $matches)) {
            $currentType = $matches[1];
            $result[] = [
                'type' => $currentType,
                'options' => []
            ];
        }
        // ตรวจสอบว่าเป็นบรรทัด index และ code หรือไม่
        elseif (preg_match('/index="(\d+)"\s+code="([^"]*)"/', $line, $matches)) {
            if (!empty($currentType)) {
                $index = $matches[1];
                $code = $matches[2];

                // ค้นหาข้อมูล Force Code
                $forceCodeInfo = null;
                if ($code !== '' && isset($forceCodesMap[$code])) {
                    $forceCodeInfo = $forceCodesMap[$code];
                }

                $optionData = [
                    'index' => $index,
                    'code' => $code,
                    'forceCodeName' => $forceCodeInfo ? $forceCodeInfo['name'] : ($code !== '' ? 'ไม่พบข้อมูล' : null),
                    'optionScroll' => $forceCodeInfo ? $forceCodeInfo['optionScroll'] : null,
                    'optionScrollName' => $forceCodeInfo ? $forceCodeInfo['optionScrollName'] : null
                ];

                // เพิ่มข้อมูลลงในประเภทปัจจุบัน
                $lastIndex = count($result) - 1;
                if ($lastIndex >= 0) {
                    $result[$lastIndex]['options'][] = $optionData;
                }
            }
        }
    }

    return $result;
}

function calculateStats($optionData) {
    $totalTypes = count($optionData);
    $totalCodes = 0;
    $mappedCodes = 0;
    $unmappedCodes = 0;
    
    foreach ($optionData as $type) {
        foreach ($type['options'] as $option) {
            if ($option['code'] !== '') {
                $totalCodes++;
                
                if ($option['forceCodeName'] && $option['forceCodeName'] !== 'ไม่พบข้อมูล') {
                    $mappedCodes++;
                } else {
                    $unmappedCodes++;
                }
            }
        }
    }
    
    return [
        'totalTypes' => $totalTypes,
        'totalCodes' => $totalCodes,
        'mappedCodes' => $mappedCodes,
        'unmappedCodes' => $unmappedCodes
    ];
}

function getForceCodesFromDB($conn) {
    try {
        $sql = "SELECT * FROM WEB_cabal_forcecodes ORDER BY ForceCode";
        $stmt = sqlsrv_query($conn, $sql);
        
        if (!$stmt) {
            echo json_encode(['success' => false, 'message' => 'ไม่สามารถดึงข้อมูลได้']);
            return;
        }
        
        $forceCodes = [];
        while ($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_ASSOC)) {
            $forceCodes[] = $row;
        }
        
        echo json_encode(['success' => true, 'data' => $forceCodes]);
        
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => 'เกิดข้อผิดพลาด: ' . $e->getMessage()]);
    }
}

function searchForceCode($conn) {
    try {
        $searchTerm = $_POST['search'] ?? '';
        
        if (empty($searchTerm)) {
            echo json_encode(['success' => false, 'message' => 'กรุณาระบุคำค้นหา']);
            return;
        }
        
        $sql = "SELECT * FROM WEB_cabal_forcecodes 
                WHERE ForceCode LIKE ? 
                OR ForceCodeName LIKE ? 
                OR OptionScrollName LIKE ?
                ORDER BY ForceCode";
        
        $searchParam = '%' . $searchTerm . '%';
        $params = [$searchParam, $searchParam, $searchParam];
        
        $stmt = sqlsrv_query($conn, $sql, $params);
        
        if (!$stmt) {
            echo json_encode(['success' => false, 'message' => 'ไม่สามารถค้นหาได้']);
            return;
        }
        
        $results = [];
        while ($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_ASSOC)) {
            $results[] = $row;
        }
        
        echo json_encode(['success' => true, 'data' => $results, 'count' => count($results)]);
        
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => 'เกิดข้อผิดพลาด: ' . $e->getMessage()]);
    }
}
?>
