<?php
session_start();
require_once("../../_app/dbinfo.inc.php");

// ตรวจสอบการ login
if (!isset($_SESSION['userLogin'])) {
    header("Location: ../../index.php");
    exit();
}

$conn = db_connect();
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Option Code Manager</title>
    <link href="../../assets/css/bootstrap.min.css" rel="stylesheet">
    <link href="../../assets/css/font-awesome.min.css" rel="stylesheet">
    <style>
        .card {
            margin-bottom: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .card-header {
            background-color: #f8f9fa;
            padding: 15px;
            border-bottom: 1px solid #ddd;
            font-weight: bold;
        }
        .card-body {
            padding: 15px;
        }
        .option-item {
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            background-color: #f9f9f9;
        }
        .option-item.has-code {
            background-color: #e8f5e8;
            border-color: #28a745;
        }
        .option-item.no-code {
            background-color: #fff3cd;
            border-color: #ffc107;
        }
        .force-code-name {
            font-weight: bold;
            color: #007bff;
        }
        .loading {
            text-align: center;
            padding: 20px;
        }
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .search-box {
            margin-bottom: 20px;
        }
        .filter-buttons {
            margin-bottom: 15px;
        }
        .btn-filter {
            margin-right: 10px;
            margin-bottom: 5px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <h2><i class="fa fa-cogs"></i> Option Code Manager</h2>
                <p class="text-muted">จัดการ Option Code จาก XML และเชื่อมโยงกับ Force Code Database</p>
                
                <!-- สถิติ -->
                <div class="row">
                    <div class="col-md-3">
                        <div class="stats-card">
                            <h4 id="totalTypes">0</h4>
                            <p>ประเภทไอเท็ม</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card">
                            <h4 id="totalCodes">0</h4>
                            <p>รหัสทั้งหมด</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card">
                            <h4 id="mappedCodes">0</h4>
                            <p>เชื่อมโยงแล้ว</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card">
                            <h4 id="unmappedCodes">0</h4>
                            <p>ยังไม่เชื่อมโยง</p>
                        </div>
                    </div>
                </div>

                <!-- ค้นหาและกรอง -->
                <div class="card">
                    <div class="card-header">
                        <i class="fa fa-search"></i> ค้นหาและกรอง
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="search-box">
                                    <input type="text" id="searchInput" class="form-control" placeholder="ค้นหาประเภทไอเท็ม หรือ รหัส...">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="filter-buttons">
                                    <button class="btn btn-primary btn-filter" onclick="filterData('all')">ทั้งหมด</button>
                                    <button class="btn btn-success btn-filter" onclick="filterData('mapped')">เชื่อมโยงแล้ว</button>
                                    <button class="btn btn-warning btn-filter" onclick="filterData('unmapped')">ยังไม่เชื่อมโยง</button>
                                    <button class="btn btn-info btn-filter" onclick="refreshData()"><i class="fa fa-refresh"></i> รีเฟรช</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- แท็บ -->
                <ul class="nav nav-tabs" id="mainTabs" role="tablist">
                    <li class="nav-item">
                        <a class="nav-link active" id="option-codes-tab" data-toggle="tab" href="#option-codes" role="tab">
                            <i class="fa fa-list"></i> Option Codes
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" id="force-codes-tab" data-toggle="tab" href="#force-codes" role="tab">
                            <i class="fa fa-database"></i> Force Codes Database
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" id="mapping-tab" data-toggle="tab" href="#mapping" role="tab">
                            <i class="fa fa-link"></i> การเชื่อมโยง
                        </a>
                    </li>
                </ul>

                <div class="tab-content" id="mainTabContent">
                    <!-- แท็บ Option Codes -->
                    <div class="tab-pane fade show active" id="option-codes" role="tabpanel">
                        <!-- ผลลัพธ์ -->
                        <div id="loadingDiv" class="loading">
                            <i class="fa fa-spinner fa-spin fa-2x"></i>
                            <p>กำลังโหลดข้อมูล...</p>
                        </div>

                        <div id="resultsContainer" style="display: none;">
                            <!-- ข้อมูลจะแสดงที่นี่ -->
                        </div>
                    </div>

                    <!-- แท็บ Force Codes Database -->
                    <div class="tab-pane fade" id="force-codes" role="tabpanel">
                        <div class="card mt-3">
                            <div class="card-header">
                                <i class="fa fa-database"></i> Force Codes Database
                                <button class="btn btn-sm btn-primary float-right" onclick="loadForceCodesData()">
                                    <i class="fa fa-refresh"></i> รีเฟรช
                                </button>
                            </div>
                            <div class="card-body">
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <input type="text" id="forceCodeSearch" class="form-control" placeholder="ค้นหา Force Code...">
                                    </div>
                                    <div class="col-md-6">
                                        <button class="btn btn-info" onclick="searchForceCodes()">
                                            <i class="fa fa-search"></i> ค้นหา
                                        </button>
                                    </div>
                                </div>
                                <div id="forceCodesTable">
                                    <!-- ตารางจะแสดงที่นี่ -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- แท็บการเชื่อมโยง -->
                    <div class="tab-pane fade" id="mapping" role="tabpanel">
                        <div class="card mt-3">
                            <div class="card-header">
                                <i class="fa fa-link"></i> สรุปการเชื่อมโยง
                            </div>
                            <div class="card-body">
                                <div id="mappingSummary">
                                    <!-- สรุปการเชื่อมโยงจะแสดงที่นี่ -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="../../assets/js/jquery.min.js"></script>
    <script src="../../assets/js/bootstrap.min.js"></script>
    <script>
        let allData = [];
        let filteredData = [];
        let currentFilter = 'all';

        $(document).ready(function() {
            loadOptionCodeData();
            
            // ค้นหา
            $('#searchInput').on('input', function() {
                performSearch();
            });
        });

        function loadOptionCodeData() {
            $('#loadingDiv').show();
            $('#resultsContainer').hide();
            
            $.ajax({
                url: 'class_module/optioncode_api.php',
                method: 'POST',
                data: { action: 'load_data' },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        allData = response.data;
                        updateStats(response.stats);
                        filterData(currentFilter);
                    } else {
                        alert('เกิดข้อผิดพลาด: ' + response.message);
                    }
                    $('#loadingDiv').hide();
                },
                error: function(xhr, status, error) {
                    console.error('AJAX Error:', error);
                    alert('เกิดข้อผิดพลาดในการโหลดข้อมูล');
                    $('#loadingDiv').hide();
                }
            });
        }

        function updateStats(stats) {
            $('#totalTypes').text(stats.totalTypes);
            $('#totalCodes').text(stats.totalCodes);
            $('#mappedCodes').text(stats.mappedCodes);
            $('#unmappedCodes').text(stats.unmappedCodes);
        }

        function filterData(filter) {
            currentFilter = filter;
            
            // อัพเดทปุ่ม
            $('.btn-filter').removeClass('active');
            $(`button[onclick="filterData('${filter}')"]`).addClass('active');
            
            switch(filter) {
                case 'mapped':
                    filteredData = allData.filter(type => 
                        type.options.some(opt => opt.forceCodeName && opt.forceCodeName !== 'ไม่พบข้อมูล')
                    );
                    break;
                case 'unmapped':
                    filteredData = allData.filter(type => 
                        type.options.some(opt => !opt.forceCodeName || opt.forceCodeName === 'ไม่พบข้อมูล')
                    );
                    break;
                default:
                    filteredData = allData;
            }
            
            performSearch();
        }

        function performSearch() {
            const searchTerm = $('#searchInput').val().toLowerCase();
            let displayData = filteredData;
            
            if (searchTerm) {
                displayData = filteredData.filter(type => 
                    type.type.toLowerCase().includes(searchTerm) ||
                    type.options.some(opt => 
                        opt.code.includes(searchTerm) ||
                        (opt.forceCodeName && opt.forceCodeName.toLowerCase().includes(searchTerm))
                    )
                );
            }
            
            displayResults(displayData);
        }

        function displayResults(data) {
            const container = $('#resultsContainer');
            container.empty();
            
            if (data.length === 0) {
                container.html('<div class="alert alert-info">ไม่พบข้อมูลที่ตรงกับเงื่อนไข</div>');
                container.show();
                return;
            }
            
            data.forEach(function(typeData) {
                const card = createTypeCard(typeData);
                container.append(card);
            });
            
            container.show();
        }

        function createTypeCard(typeData) {
            let optionsHtml = '';
            
            typeData.options.forEach(function(option) {
                const hasCode = option.code && option.code !== '';
                const hasMapping = option.forceCodeName && option.forceCodeName !== 'ไม่พบข้อมูล';
                const cssClass = hasCode ? (hasMapping ? 'has-code' : 'no-code') : 'no-code';
                
                optionsHtml += `
                    <div class="option-item ${cssClass}">
                        <div class="row">
                            <div class="col-md-2">
                                <strong>Index:</strong> ${option.index}
                            </div>
                            <div class="col-md-2">
                                <strong>Code:</strong> ${option.code || 'ไม่มี'}
                            </div>
                            <div class="col-md-4">
                                <strong>Force Code Name:</strong> 
                                <span class="force-code-name">${option.forceCodeName || 'ไม่พบข้อมูล'}</span>
                            </div>
                            <div class="col-md-4">
                                <strong>Option Scroll:</strong> ${option.optionScroll || 'ไม่มี'}
                            </div>
                        </div>
                    </div>
                `;
            });
            
            return `
                <div class="card">
                    <div class="card-header">
                        <i class="fa fa-cube"></i> ${typeData.type}
                        <span class="badge badge-primary float-right">${typeData.options.length} รายการ</span>
                    </div>
                    <div class="card-body">
                        ${optionsHtml}
                    </div>
                </div>
            `;
        }

        function refreshData() {
            loadOptionCodeData();
        }

        // ฟังก์ชันสำหรับ Force Codes Database
        function loadForceCodesData() {
            $.ajax({
                url: 'class_module/optioncode_api.php',
                method: 'POST',
                data: { action: 'get_force_codes' },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        displayForceCodesTable(response.data);
                    } else {
                        $('#forceCodesTable').html('<div class="alert alert-danger">เกิดข้อผิดพลาด: ' + response.message + '</div>');
                    }
                },
                error: function(xhr, status, error) {
                    $('#forceCodesTable').html('<div class="alert alert-danger">เกิดข้อผิดพลาดในการโหลดข้อมูล</div>');
                }
            });
        }

        function displayForceCodesTable(data) {
            if (data.length === 0) {
                $('#forceCodesTable').html('<div class="alert alert-info">ไม่พบข้อมูล Force Codes</div>');
                return;
            }

            let tableHtml = `
                <div class="table-responsive">
                    <table class="table table-striped table-bordered">
                        <thead class="thead-dark">
                            <tr>
                                <th>Force Code</th>
                                <th>Force Code Name</th>
                                <th>Option Scroll</th>
                                <th>Option Scroll Name</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

            data.forEach(function(item) {
                tableHtml += `
                    <tr>
                        <td><strong>${item.ForceCode}</strong></td>
                        <td>${item.ForceCodeName || 'ไม่มี'}</td>
                        <td>${item.OptionScroll}</td>
                        <td>${item.OptionScrollName}</td>
                    </tr>
                `;
            });

            tableHtml += `
                        </tbody>
                    </table>
                </div>
                <p class="text-muted">รวม ${data.length} รายการ</p>
            `;

            $('#forceCodesTable').html(tableHtml);
        }

        function searchForceCodes() {
            const searchTerm = $('#forceCodeSearch').val();

            if (!searchTerm.trim()) {
                loadForceCodesData();
                return;
            }

            $.ajax({
                url: 'class_module/optioncode_api.php',
                method: 'POST',
                data: {
                    action: 'search_code',
                    search: searchTerm
                },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        displayForceCodesTable(response.data);
                    } else {
                        $('#forceCodesTable').html('<div class="alert alert-danger">เกิดข้อผิดพลาด: ' + response.message + '</div>');
                    }
                },
                error: function(xhr, status, error) {
                    $('#forceCodesTable').html('<div class="alert alert-danger">เกิดข้อผิดพลาดในการค้นหา</div>');
                }
            });
        }

        // Event listener สำหรับ Enter key ในช่องค้นหา Force Code
        $('#forceCodeSearch').on('keypress', function(e) {
            if (e.which === 13) {
                searchForceCodes();
            }
        });

        // โหลดข้อมูล Force Codes เมื่อเปลี่ยนแท็บ
        $('a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
            const target = $(e.target).attr("href");
            if (target === '#force-codes') {
                loadForceCodesData();
            } else if (target === '#mapping') {
                generateMappingSummary();
            }
        });

        function generateMappingSummary() {
            if (allData.length === 0) {
                $('#mappingSummary').html('<div class="alert alert-info">กรุณาโหลดข้อมูล Option Codes ก่อน</div>');
                return;
            }

            let summaryHtml = '<h5>สรุปการเชื่อมโยงข้อมูล</h5>';

            allData.forEach(function(typeData) {
                const mappedCount = typeData.options.filter(opt =>
                    opt.forceCodeName && opt.forceCodeName !== 'ไม่พบข้อมูล'
                ).length;

                const totalWithCode = typeData.options.filter(opt => opt.code !== '').length;

                const percentage = totalWithCode > 0 ? Math.round((mappedCount / totalWithCode) * 100) : 0;

                summaryHtml += `
                    <div class="card mb-2">
                        <div class="card-body">
                            <h6>${typeData.type}</h6>
                            <div class="progress mb-2">
                                <div class="progress-bar ${percentage === 100 ? 'bg-success' : percentage > 50 ? 'bg-warning' : 'bg-danger'}"
                                     style="width: ${percentage}%">${percentage}%</div>
                            </div>
                            <small class="text-muted">
                                เชื่อมโยงแล้ว: ${mappedCount}/${totalWithCode} รายการ
                            </small>
                        </div>
                    </div>
                `;
            });

            $('#mappingSummary').html(summaryHtml);
        }
    </script>
</body>
</html>
