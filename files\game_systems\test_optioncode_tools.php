<?php
session_start();
require_once("../../_app/dbinfo.inc.php");

// ตรวจสอบการ login
if (!isset($_SESSION['userLogin'])) {
    header("Location: ../../index.php");
    exit();
}

$conn = db_connect();
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ทดสอบ Option Code Tools</title>
    <link href="../../assets/css/bootstrap.min.css" rel="stylesheet">
    <link href="../../assets/css/font-awesome.min.css" rel="stylesheet">
    <style>
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-result {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-top: 10px;
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
        .info { color: #17a2b8; }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <h2><i class="fa fa-flask"></i> ทดสอบ Option Code Tools</h2>
                <p class="text-muted">ทดสอบการทำงานของระบบ Option Code Manager</p>

                <!-- ทดสอบการอ่านไฟล์ XML -->
                <div class="test-section">
                    <h4><i class="fa fa-file-code-o"></i> ทดสอบการอ่านไฟล์ optioncode.xml</h4>
                    <button class="btn btn-primary" onclick="testXMLReading()">ทดสอบ</button>
                    <div id="xmlTestResult" class="test-result" style="display: none;"></div>
                </div>

                <!-- ทดสอบการเชื่อมต่อ Database -->
                <div class="test-section">
                    <h4><i class="fa fa-database"></i> ทดสอบการเชื่อมต่อ WEB_cabal_forcecodes</h4>
                    <button class="btn btn-primary" onclick="testDatabaseConnection()">ทดสอบ</button>
                    <div id="dbTestResult" class="test-result" style="display: none;"></div>
                </div>

                <!-- ทดสอบ API -->
                <div class="test-section">
                    <h4><i class="fa fa-cogs"></i> ทดสอบ API</h4>
                    <div class="row">
                        <div class="col-md-4">
                            <button class="btn btn-info btn-block" onclick="testLoadDataAPI()">ทดสอบ load_data</button>
                        </div>
                        <div class="col-md-4">
                            <button class="btn btn-info btn-block" onclick="testForceCodesAPI()">ทดสอบ get_force_codes</button>
                        </div>
                        <div class="col-md-4">
                            <button class="btn btn-info btn-block" onclick="testSearchAPI()">ทดสอบ search_code</button>
                        </div>
                    </div>
                    <div id="apiTestResult" class="test-result" style="display: none;"></div>
                </div>

                <!-- ทดสอบการเชื่อมโยงข้อมูล -->
                <div class="test-section">
                    <h4><i class="fa fa-link"></i> ทดสอบการเชื่อมโยงข้อมูล</h4>
                    <button class="btn btn-success" onclick="testDataMapping()">ทดสอบ</button>
                    <div id="mappingTestResult" class="test-result" style="display: none;"></div>
                </div>

                <!-- สรุปผลการทดสอบ -->
                <div class="test-section">
                    <h4><i class="fa fa-check-circle"></i> สรุปผลการทดสอบ</h4>
                    <div id="summaryResult" class="test-result">
                        <p class="info">กรุณาทำการทดสอบแต่ละส่วน</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="../../assets/js/jquery.min.js"></script>
    <script src="../../assets/js/bootstrap.min.js"></script>
    <script>
        let testResults = {
            xml: false,
            database: false,
            api: false,
            mapping: false
        };

        function testXMLReading() {
            $('#xmlTestResult').show().html('<i class="fa fa-spinner fa-spin"></i> กำลังทดสอบ...');
            
            // ทดสอบการอ่านไฟล์ XML โดยตรง
            $.get('optioncode.xml')
                .done(function(data) {
                    const xmlDoc = $.parseXML(data);
                    const lines = data.split('\n');
                    let typeCount = 0;
                    let codeCount = 0;
                    
                    lines.forEach(function(line) {
                        if (line.includes('type=')) typeCount++;
                        if (line.includes('code=') && !line.includes('code=""')) codeCount++;
                    });
                    
                    $('#xmlTestResult').html(`
                        <p class="success"><i class="fa fa-check"></i> ✅ อ่านไฟล์ XML สำเร็จ</p>
                        <ul>
                            <li>พบประเภทไอเท็ม: ${typeCount} ประเภท</li>
                            <li>พบรหัส: ${codeCount} รหัส</li>
                            <li>ขนาดไฟล์: ${data.length} ตัวอักษร</li>
                        </ul>
                    `);
                    testResults.xml = true;
                    updateSummary();
                })
                .fail(function() {
                    $('#xmlTestResult').html('<p class="error"><i class="fa fa-times"></i> ❌ ไม่สามารถอ่านไฟล์ XML ได้</p>');
                    testResults.xml = false;
                    updateSummary();
                });
        }

        function testDatabaseConnection() {
            $('#dbTestResult').show().html('<i class="fa fa-spinner fa-spin"></i> กำลังทดสอบ...');
            
            $.ajax({
                url: 'class_module/optioncode_api.php',
                method: 'POST',
                data: { action: 'get_force_codes' },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        $('#dbTestResult').html(`
                            <p class="success"><i class="fa fa-check"></i> ✅ เชื่อมต่อ Database สำเร็จ</p>
                            <ul>
                                <li>พบข้อมูล Force Codes: ${response.data.length} รายการ</li>
                                <li>ตาราง: WEB_cabal_forcecodes</li>
                                <li>สถานะ: เชื่อมต่อปกติ</li>
                            </ul>
                        `);
                        testResults.database = true;
                    } else {
                        $('#dbTestResult').html(`<p class="error"><i class="fa fa-times"></i> ❌ เกิดข้อผิดพลาด: ${response.message}</p>`);
                        testResults.database = false;
                    }
                    updateSummary();
                },
                error: function() {
                    $('#dbTestResult').html('<p class="error"><i class="fa fa-times"></i> ❌ ไม่สามารถเชื่อมต่อ Database ได้</p>');
                    testResults.database = false;
                    updateSummary();
                }
            });
        }

        function testLoadDataAPI() {
            $('#apiTestResult').show().html('<i class="fa fa-spinner fa-spin"></i> กำลังทดสอบ load_data API...');
            
            $.ajax({
                url: 'class_module/optioncode_api.php',
                method: 'POST',
                data: { action: 'load_data' },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        $('#apiTestResult').html(`
                            <p class="success"><i class="fa fa-check"></i> ✅ load_data API ทำงานสำเร็จ</p>
                            <ul>
                                <li>ประเภทไอเท็ม: ${response.stats.totalTypes}</li>
                                <li>รหัสทั้งหมด: ${response.stats.totalCodes}</li>
                                <li>เชื่อมโยงแล้ว: ${response.stats.mappedCodes}</li>
                                <li>ยังไม่เชื่อมโยง: ${response.stats.unmappedCodes}</li>
                            </ul>
                        `);
                        testResults.api = true;
                    } else {
                        $('#apiTestResult').html(`<p class="error"><i class="fa fa-times"></i> ❌ API Error: ${response.message}</p>`);
                        testResults.api = false;
                    }
                    updateSummary();
                },
                error: function() {
                    $('#apiTestResult').html('<p class="error"><i class="fa fa-times"></i> ❌ ไม่สามารถเรียก API ได้</p>');
                    testResults.api = false;
                    updateSummary();
                }
            });
        }

        function testForceCodesAPI() {
            $.ajax({
                url: 'class_module/optioncode_api.php',
                method: 'POST',
                data: { action: 'get_force_codes' },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        $('#apiTestResult').append(`
                            <p class="success"><i class="fa fa-check"></i> ✅ get_force_codes API ทำงานสำเร็จ (${response.data.length} รายการ)</p>
                        `);
                    }
                }
            });
        }

        function testSearchAPI() {
            $.ajax({
                url: 'class_module/optioncode_api.php',
                method: 'POST',
                data: { action: 'search_code', search: 'HP' },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        $('#apiTestResult').append(`
                            <p class="success"><i class="fa fa-check"></i> ✅ search_code API ทำงานสำเร็จ (พบ ${response.count} รายการ)</p>
                        `);
                    }
                }
            });
        }

        function testDataMapping() {
            $('#mappingTestResult').show().html('<i class="fa fa-spinner fa-spin"></i> กำลังทดสอบการเชื่อมโยงข้อมูล...');
            
            $.ajax({
                url: 'class_module/optioncode_api.php',
                method: 'POST',
                data: { action: 'load_data' },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        let mappingDetails = '';
                        let totalMapped = 0;
                        let totalCodes = 0;
                        
                        response.data.forEach(function(type) {
                            let typeMapped = 0;
                            let typeCodes = 0;
                            
                            type.options.forEach(function(option) {
                                if (option.code !== '') {
                                    typeCodes++;
                                    totalCodes++;
                                    if (option.forceCodeName && option.forceCodeName !== 'ไม่พบข้อมูล') {
                                        typeMapped++;
                                        totalMapped++;
                                    }
                                }
                            });
                            
                            if (typeCodes > 0) {
                                const percentage = Math.round((typeMapped / typeCodes) * 100);
                                mappingDetails += `<li>${type.type}: ${typeMapped}/${typeCodes} (${percentage}%)</li>`;
                            }
                        });
                        
                        const overallPercentage = totalCodes > 0 ? Math.round((totalMapped / totalCodes) * 100) : 0;
                        
                        $('#mappingTestResult').html(`
                            <p class="success"><i class="fa fa-check"></i> ✅ การเชื่อมโยงข้อมูลทำงานสำเร็จ</p>
                            <h6>สรุปการเชื่อมโยง (${overallPercentage}%):</h6>
                            <ul>${mappingDetails}</ul>
                        `);
                        testResults.mapping = true;
                    } else {
                        $('#mappingTestResult').html(`<p class="error"><i class="fa fa-times"></i> ❌ เกิดข้อผิดพลาด: ${response.message}</p>`);
                        testResults.mapping = false;
                    }
                    updateSummary();
                },
                error: function() {
                    $('#mappingTestResult').html('<p class="error"><i class="fa fa-times"></i> ❌ ไม่สามารถทดสอบการเชื่อมโยงได้</p>');
                    testResults.mapping = false;
                    updateSummary();
                }
            });
        }

        function updateSummary() {
            const passedTests = Object.values(testResults).filter(result => result === true).length;
            const totalTests = Object.keys(testResults).length;
            
            let summaryHtml = `
                <h6>ผลการทดสอบ: ${passedTests}/${totalTests} ผ่าน</h6>
                <ul>
                    <li>การอ่านไฟล์ XML: ${testResults.xml ? '<span class="success">✅ ผ่าน</span>' : '<span class="error">❌ ไม่ผ่าน</span>'}</li>
                    <li>การเชื่อมต่อ Database: ${testResults.database ? '<span class="success">✅ ผ่าน</span>' : '<span class="error">❌ ไม่ผ่าน</span>'}</li>
                    <li>การทำงานของ API: ${testResults.api ? '<span class="success">✅ ผ่าน</span>' : '<span class="error">❌ ไม่ผ่าน</span>'}</li>
                    <li>การเชื่อมโยงข้อมูล: ${testResults.mapping ? '<span class="success">✅ ผ่าน</span>' : '<span class="error">❌ ไม่ผ่าน</span>'}</li>
                </ul>
            `;
            
            if (passedTests === totalTests) {
                summaryHtml += '<p class="success"><strong>🎉 ระบบพร้อมใช้งาน!</strong></p>';
                summaryHtml += '<a href="optioncode_manager.php" class="btn btn-success"><i class="fa fa-arrow-right"></i> ไปยัง Option Code Manager</a>';
            } else {
                summaryHtml += '<p class="warning"><strong>⚠️ กรุณาแก้ไขปัญหาก่อนใช้งาน</strong></p>';
            }
            
            $('#summaryResult').html(summaryHtml);
        }
    </script>
</body>
</html>
