<?php
session_start();
require_once("../../_app/dbinfo.inc.php");

// ตรวจสอบการ login
if (!isset($_SESSION['userLogin'])) {
    header("Location: ../../index.php");
    exit();
}
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Option Code Tools</title>
    <link href="../../assets/vendor/bootstrap/css/bootstrap.css" rel="stylesheet">
    <link href="../../assets/vendor/font-awesome/css/all.min.css" rel="stylesheet">
    <style>
        .tool-card {
            transition: transform 0.2s;
            height: 100%;
        }
        .tool-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .tool-icon {
            font-size: 3rem;
            margin-bottom: 20px;
        }
        .status-badge {
            position: absolute;
            top: 10px;
            right: 10px;
        }
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 60px 0;
            margin-bottom: 40px;
        }
        .feature-list {
            list-style: none;
            padding: 0;
        }
        .feature-list li {
            padding: 5px 0;
        }
        .feature-list li:before {
            content: "✓";
            color: #28a745;
            font-weight: bold;
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <!-- Hero Section -->
    <div class="hero-section">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center">
                    <h1><i class="fa fa-cogs"></i> Option Code Tools</h1>
                    <p class="lead">เครื่องมือจัดการ Option Code และ Force Code Database</p>
                    <p>เชื่อมโยงข้อมูลจาก optioncode.xml กับฐานข้อมูล WEB_cabal_forcecodes</p>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- เครื่องมือหลัก -->
        <div class="row">
            <div class="col-md-4 mb-4">
                <div class="card tool-card">
                    <span class="badge badge-success status-badge">พร้อมใช้งาน</span>
                    <div class="card-body text-center">
                        <div class="tool-icon text-primary">
                            <i class="fa fa-dashboard"></i>
                        </div>
                        <h5 class="card-title">Option Code Manager</h5>
                        <p class="card-text">หน้าหลักสำหรับจัดการ Option Code และดูการเชื่อมโยงข้อมูล</p>
                        <ul class="feature-list text-left">
                            <li>แสดงข้อมูลจาก XML</li>
                            <li>เชื่อมโยงกับ Force Code</li>
                            <li>สถิติและการกรองข้อมูล</li>
                            <li>ค้นหาและแสดงผล</li>
                        </ul>
                        <a href="optioncode_manager.php" class="btn btn-primary btn-block">
                            <i class="fa fa-arrow-right"></i> เข้าใช้งาน
                        </a>
                    </div>
                </div>
            </div>

            <div class="col-md-4 mb-4">
                <div class="card tool-card">
                    <span class="badge badge-info status-badge">ทดสอบ</span>
                    <div class="card-body text-center">
                        <div class="tool-icon text-warning">
                            <i class="fa fa-flask"></i>
                        </div>
                        <h5 class="card-title">System Test</h5>
                        <p class="card-text">ทดสอบการทำงานของระบบก่อนใช้งานจริง</p>
                        <ul class="feature-list text-left">
                            <li>ทดสอบการอ่านไฟล์ XML</li>
                            <li>ทดสอบการเชื่อมต่อ Database</li>
                            <li>ทดสอบ API ทั้งหมด</li>
                            <li>ตรวจสอบการเชื่อมโยงข้อมูล</li>
                        </ul>
                        <a href="test_optioncode_tools.php" class="btn btn-warning btn-block">
                            <i class="fa fa-play"></i> เริ่มทดสอบ
                        </a>
                    </div>
                </div>
            </div>

            <div class="col-md-4 mb-4">
                <div class="card tool-card">
                    <span class="badge badge-secondary status-badge">เอกสาร</span>
                    <div class="card-body text-center">
                        <div class="tool-icon text-info">
                            <i class="fa fa-book"></i>
                        </div>
                        <h5 class="card-title">Documentation</h5>
                        <p class="card-text">คู่มือการใช้งานและการติดตั้งระบบ</p>
                        <ul class="feature-list text-left">
                            <li>วิธีการใช้งาน</li>
                            <li>โครงสร้างฐานข้อมูล</li>
                            <li>การแก้ไขปัญหา</li>
                            <li>การพัฒนาเพิ่มเติม</li>
                        </ul>
                        <a href="OPTIONCODE_TOOLS_README.md" class="btn btn-info btn-block" target="_blank">
                            <i class="fa fa-external-link"></i> อ่านเอกสาร
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- ข้อมูลระบบ -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fa fa-info-circle"></i> ข้อมูลระบบ</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>ไฟล์ที่เกี่ยวข้อง:</h6>
                                <ul>
                                    <li><code>optioncode.xml</code> - ไฟล์ข้อมูล Option Code (XML)</li>
                                    <li><code>optioncode_manager.php</code> - หน้าจัดการหลัก</li>
                                    <li><code>class_module/optioncode_api.php</code> - API ประมวลผล</li>
                                    <li><code>test_optioncode_tools.php</code> - หน้าทดสอบระบบ</li>
                                    <li><code>test_xml_structure.php</code> - ทดสอบโครงสร้าง XML</li>
                                    <li><code>quick_test.php</code> - ทดสอบเบื้องต้น</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>ฐานข้อมูล:</h6>
                                <ul>
                                    <li><strong>ตาราง:</strong> WEB_cabal_forcecodes</li>
                                    <li><strong>ฟิลด์หลัก:</strong> ForceCode, ForceCodeName</li>
                                    <li><strong>การเชื่อมโยง:</strong> XML code = DB ForceCode</li>
                                </ul>
                                
                                <h6 class="mt-3">สถานะระบบ:</h6>
                                <div id="systemStatus">
                                    <span class="badge badge-secondary">กำลังตรวจสอบ...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- การติดตั้ง -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fa fa-download"></i> การติดตั้งและตั้งค่า</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>ขั้นตอนการติดตั้ง:</h6>
                                <ol>
                                    <li>ตรวจสอบไฟล์ <code>optioncode.xml</code></li>
                                    <li>รันสคริปต์ SQL สร้างตาราง</li>
                                    <li>ทดสอบระบบ</li>
                                    <li>เริ่มใช้งาน</li>
                                </ol>
                                
                                <div class="mt-3">
                                    <a href="setup_forcecodes_table.sql" class="btn btn-success btn-sm" download>
                                        <i class="fa fa-download"></i> ดาวน์โหลด SQL Script
                                    </a>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6>การตรวจสอบระบบ:</h6>
                                <div class="list-group">
                                    <div class="list-group-item d-flex justify-content-between align-items-center">
                                        ไฟล์ XML
                                        <span id="xmlStatus" class="badge badge-secondary">ตรวจสอบ</span>
                                    </div>
                                    <div class="list-group-item d-flex justify-content-between align-items-center">
                                        ฐานข้อมูล
                                        <span id="dbStatus" class="badge badge-secondary">ตรวจสอบ</span>
                                    </div>
                                    <div class="list-group-item d-flex justify-content-between align-items-center">
                                        API
                                        <span id="apiStatus" class="badge badge-secondary">ตรวจสอบ</span>
                                    </div>
                                </div>
                                
                                <div class="mt-3">
                                    <button class="btn btn-primary btn-sm" onclick="checkSystemStatus()">
                                        <i class="fa fa-refresh"></i> ตรวจสอบสถานะ
                                    </button>
                                    <a href="test_xml_structure.php" class="btn btn-success btn-sm ml-2">
                                        <i class="fa fa-code"></i> ทดสอบ XML
                                    </a>
                                    <a href="check_database.php" class="btn btn-info btn-sm ml-2" target="_blank">
                                        <i class="fa fa-database"></i> ตรวจสอบ DB
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="../../assets/vendor/jquery/jquery.js"></script>
    <script src="../../assets/vendor/bootstrap/js/bootstrap.js"></script>
    <script>
        $(document).ready(function() {
            checkSystemStatus();
        });

        function checkSystemStatus() {
            // ตรวจสอบไฟล์ XML
            $.get('optioncode.xml')
                .done(function() {
                    $('#xmlStatus').removeClass('badge-secondary badge-danger').addClass('badge-success').text('พร้อม');
                })
                .fail(function() {
                    $('#xmlStatus').removeClass('badge-secondary badge-success').addClass('badge-danger').text('ไม่พบ');
                });

            // ตรวจสอบฐานข้อมูล
            $.ajax({
                url: 'class_module/optioncode_api.php',
                method: 'POST',
                data: { action: 'get_force_codes' },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        $('#dbStatus').removeClass('badge-secondary badge-danger').addClass('badge-success').text('เชื่อมต่อ');
                    } else {
                        $('#dbStatus').removeClass('badge-secondary badge-success').addClass('badge-danger').text('ข้อผิดพลาด');
                    }
                },
                error: function() {
                    $('#dbStatus').removeClass('badge-secondary badge-success').addClass('badge-danger').text('ไม่เชื่อมต่อ');
                }
            });

            // ตรวจสอบ API
            $.ajax({
                url: 'class_module/optioncode_api.php',
                method: 'POST',
                data: { action: 'load_data' },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        $('#apiStatus').removeClass('badge-secondary badge-danger').addClass('badge-success').text('ทำงาน');
                        updateSystemStatus('success', `ระบบพร้อมใช้งาน (${response.stats.totalTypes} ประเภท, ${response.stats.totalCodes} รหัส)`);
                    } else {
                        $('#apiStatus').removeClass('badge-secondary badge-success').addClass('badge-danger').text('ข้อผิดพลาด');
                        updateSystemStatus('danger', 'API มีปัญหา');
                    }
                },
                error: function() {
                    $('#apiStatus').removeClass('badge-secondary badge-success').addClass('badge-danger').text('ไม่ทำงาน');
                    updateSystemStatus('danger', 'ไม่สามารถเชื่อมต่อ API ได้');
                }
            });
        }

        function updateSystemStatus(type, message) {
            const badgeClass = type === 'success' ? 'badge-success' : 'badge-danger';
            $('#systemStatus').html(`<span class="badge ${badgeClass}">${message}</span>`);
        }
    </script>
</body>
</html>
